@echo off
title Tools Downloader
cd /d "%~dp0"

echo 🛠️ Tools Downloader
echo ===================
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python not found! Install from: https://python.org
        pause
        exit /b 1
    )
    set CMD=py
) else (
    set CMD=python
)

echo ✅ Python found
echo.

REM Check GUI files
if not exist "gui.py" (
    echo ❌ GUI files missing!
    pause
    exit /b 1
)

echo  Starting Tools Downloader GUI...
echo.
%CMD% launcher.py

echo.
pause
